const mongoose = require('mongoose');

// Connect to MongoDB Atlas
const DB_URL = "mongodb+srv://infosarthaktech:<EMAIL>/test";

mongoose.connect(DB_URL, {
  serverSelectionTimeoutMS: 30000,
  socketTimeoutMS: 45000,
  connectTimeoutMS: 30000
});

// Use the actual model
const CarpetReceived = require('./src/model/phase-4/carpetReceived');

async function verify210x51Entries() {
  try {
    console.log('🔍 Verifying updated entries with "2.10 X 5.1" size...');
    
    // List of receiveNo that were updated
    const updatedReceiveNos = [
      'H-210991', 'H-220014', 'H-220062', 'H-220084', 
      'H-220149', 'H-220061', 'H-170063', 'H-180014'
    ];
    
    for (const receiveNo of updatedReceiveNos) {
      const entry = await CarpetReceived.findOne({ receiveNo: receiveNo });
      
      if (entry) {
        console.log(`✅ ${receiveNo}: size = "${entry.size}"`);
        if (entry.issueNo && entry.issueNo.size) {
          console.log(`   issueNo.size.sizeInYard = "${entry.issueNo.size.sizeInYard}"`);
          console.log(`   issueNo.size.sizeinMeter = "${entry.issueNo.size.sizeinMeter}"`);
        }
      } else {
        console.log(`❌ ${receiveNo}: Not found`);
      }
    }
    
    console.log('\n🎉 Verification complete!');
    
  } catch (error) {
    console.error('❌ Error:', error);
  } finally {
    mongoose.connection.close();
  }
}

verify210x51Entries();
