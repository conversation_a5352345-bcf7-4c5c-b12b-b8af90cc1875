<div class="container mt-4">
  <section >
      <fieldset>
          <legend><b>Carpet Receiving Accounts</b></legend>
<div class="filter-section">
<div class="row">
  <!-- Search Filter -->
  <div class="col-md-3">
    <mat-form-field appearance="outline" class="w-100">
      <mat-label>Search</mat-label>
      <input matInput (keyup)="applyGeneralFilter($event)" placeholder="Search all fields...">
      <mat-icon matSuffix>search</mat-icon>
    </mat-form-field>
  </div>

  <!-- Weaver Filter -->
  <div class="col-md-3">
    <mat-form-field appearance="outline" class="w-100">
      <mat-label>Filter by <PERSON></mat-label>
      <mat-select (selectionChange)="applyWeaverFilter($event.value)" [value]="selectedWeaver">
        <mat-option value="">All Weavers</mat-option>
        <mat-option *ngFor="let weaver of uniqueWeavers" [value]="weaver">
          {{weaver}}
        </mat-option>
      </mat-select>
      <mat-icon matSuffix>person</mat-icon>
    </mat-form-field>
  </div>

  <!-- Month Filter -->
  <div class="col-md-3">
    <mat-form-field appearance="outline" class="w-100">
      <mat-label>Filter by Month</mat-label>
      <mat-select (selectionChange)="applyMonthFilter($event.value)" [value]="selectedMonth">
        <mat-option value="">All Months</mat-option>
        <mat-option value="01">January</mat-option>
        <mat-option value="02">February</mat-option>
        <mat-option value="03">March</mat-option>
        <mat-option value="04">April</mat-option>
        <mat-option value="05">May</mat-option>
        <mat-option value="06">June</mat-option>
        <mat-option value="07">July</mat-option>
        <mat-option value="08">August</mat-option>
        <mat-option value="09">September</mat-option>
        <mat-option value="10">October</mat-option>
        <mat-option value="11">November</mat-option>
        <mat-option value="12">December</mat-option>
      </mat-select>
      <mat-icon matSuffix>calendar_month</mat-icon>
    </mat-form-field>
  </div>
</div> <!-- Close row div -->
</div> <!-- Close filter-section div -->

  <!-- Loading Indicator -->
  <div *ngIf="isLoading" class="text-center p-4">
    <mat-spinner diameter="50"></mat-spinner>
    <p class="mt-2">Loading carpet data...</p>
  </div>

  <!-- Error Message -->
  <div *ngIf="errorMessage && !isLoading" class="alert alert-warning text-center">
    <mat-icon>warning</mat-icon>
    {{ errorMessage }}
  </div>

  <!-- Data Table -->
  <div *ngIf="!isLoading && !errorMessage" class="col-md-12 scroll-container" style="overflow: auto;">
    <table mat-table [dataSource]="dataSource" matSort class="mat-elevation-z8" style="width: max-content;">
      <ng-container matColumnDef="SrNo">
        <th mat-header-cell *matHeaderCellDef> Sr.No </th>
        <td mat-cell *matCellDef="let element"> {{element.SrNo}} </td>
      </ng-container>

      <ng-container matColumnDef="CarpetNo">
        <th mat-header-cell *matHeaderCellDef mat-sort-header> CarpetNo </th>
        <td mat-cell *matCellDef="let element"> {{element.CarpetNo}} </td>
      </ng-container>

      <ng-container matColumnDef="CDate">
        <th mat-header-cell *matHeaderCellDef mat-sort-header> C Date </th>
        <td mat-cell *matCellDef="let element"> {{element.CDate}} </td>
      </ng-container>

      <ng-container matColumnDef="IssueNo">
        <th mat-header-cell *matHeaderCellDef mat-sort-header> IssueNo </th>
        <td mat-cell *matCellDef="let element"> {{element.IssueNo}} </td>
      </ng-container>

      <ng-container matColumnDef="IDate">
        <th mat-header-cell *matHeaderCellDef mat-sort-header> I Date </th>
        <td mat-cell *matCellDef="let element"> {{element.IDate}} </td>
      </ng-container>

      <ng-container matColumnDef="Weaver">
        <th mat-header-cell *matHeaderCellDef mat-sort-header> Weaver </th>
        <td mat-cell *matCellDef="let element"> {{element.Weaver}} </td>
      </ng-container>

      <ng-container matColumnDef="Quality">
        <th mat-header-cell *matHeaderCellDef mat-sort-header class="text-center"> Quality </th>
        <td mat-cell *matCellDef="let element" class="text-center"> {{element.Quality}} </td>
      </ng-container>

      <ng-container matColumnDef="Design">
        <th mat-header-cell *matHeaderCellDef mat-sort-header> Design </th>
        <td mat-cell *matCellDef="let element"> {{element.Design}} </td>
      </ng-container>

      <ng-container matColumnDef="BorderColour">
        <th mat-header-cell *matHeaderCellDef mat-sort-header> Colour </th>
        <td mat-cell *matCellDef="let element"> {{element.BorderColour}} </td>
      </ng-container>

      <ng-container matColumnDef="Size">
        <th mat-header-cell *matHeaderCellDef mat-sort-header class="text-center "> Size </th>
        <td mat-cell *matCellDef="let element" class="text-center"> {{element.Size || 'N/A'}} </td>
      </ng-container>

      <ng-container matColumnDef="Pcs">
        <th mat-header-cell *matHeaderCellDef mat-sort-header class="text-center"> Pcs </th>
        <td mat-cell *matCellDef="let element" class="col-pcs text-center"> {{element.Pcs}} </td>
      </ng-container>

      <ng-container matColumnDef="Area">
        <th mat-header-cell *matHeaderCellDef mat-sort-header class="text-center"> Area </th>
        <td mat-cell *matCellDef="let element" class="col-area text-center" > {{element.Area}} </td>
      </ng-container>

      <ng-container matColumnDef="Rate">
        <th mat-header-cell *matHeaderCellDef mat-sort-header class="text-center"> Rate </th>
        <td mat-cell *matCellDef="let element" class="text-center"> {{element.Rate | number:'1.2-3'}} </td>
      </ng-container>

      <ng-container matColumnDef="Deduction">
        <th mat-header-cell *matHeaderCellDef mat-sort-header class="text-center"> Deduction </th>
        <td mat-cell *matCellDef="let element" class="text-center"> {{element.Deduction || '0.00' | number:'1.2-2'}} </td>
      </ng-container>

      <ng-container matColumnDef="Amount">
        <th mat-header-cell *matHeaderCellDef mat-sort-header class="text-center"> Amount </th>
        <td mat-cell *matCellDef="let element" class="text-center"> {{element.Amount | number:'1.0-0' }}.00 </td>
      </ng-container>

      <ng-container matColumnDef="TDS">
        <th mat-header-cell *matHeaderCellDef mat-sort-header class="text-center"> TDS </th>
        <td mat-cell *matCellDef="let element" class="text-center"> {{element.TDS | number:'1.0-0' }}.00 </td>
      </ng-container>

      <ng-container matColumnDef="Commission">
        <th mat-header-cell *matHeaderCellDef mat-sort-header class="text-center"> Commission </th>
        <td mat-cell *matCellDef="let element" class="text-center"> {{element.Commission | number:'1.0-0' }}.00 </td>
      </ng-container>

      <ng-container matColumnDef="NetAmount">
        <th mat-header-cell *matHeaderCellDef mat-sort-header class="text-center"> Net Amount </th>
        <td mat-cell *matCellDef="let element" class="text-center"> {{element.NetAmount | number:'1.0-0'}}.00 </td>
      </ng-container>

      <ng-container matColumnDef="PaymentDt">
        <th mat-header-cell *matHeaderCellDef mat-sort-header> Payment Dt </th>
        <td mat-cell *matCellDef="let element"> {{(element.PaymentDt === '01.01.1970' || !element.PaymentDt) ? '' : element.PaymentDt}} </td>
      </ng-container>

      <ng-container matColumnDef="BankName">
        <th mat-header-cell *matHeaderCellDef mat-sort-header> Bank</th>
        <td mat-cell *matCellDef="let element"> {{element.BankName}} </td>
      </ng-container>
      <ng-container matColumnDef="ChequeNoOrRGSno">
        <th mat-header-cell *matHeaderCellDef mat-sort-header> Cheque No./RGS No. </th>
        <td mat-cell *matCellDef="let element"> {{element.ChequeNoOrRGSno}} </td>
      </ng-container>
      <ng-container matColumnDef="PayedAmount">
        <th mat-header-cell *matHeaderCellDef mat-sort-header> PayedAmount </th>
        <td mat-cell *matCellDef="let element"> {{element.PayedAmount}} </td>
      </ng-container>
      <ng-container matColumnDef="Remarks">
        <th mat-header-cell *matHeaderCellDef mat-sort-header> Remarks </th>
        <td mat-cell *matCellDef="let element"> {{element.Remarks}} </td>
      </ng-container>

      <ng-container matColumnDef="Actions">
        <th mat-header-cell *matHeaderCellDef> Actions </th>
        <td mat-cell *matCellDef="let element">
          <button mat-icon-button color="primary" (click)="openEditModal(element)" title="Edit Carpet">
            <mat-icon><i class="fa-solid fa-pen-fancy"></i></mat-icon>
          </button>
          <button mat-icon-button color="accent" (click)="openPaymentModal(element)" title="Edit Payment"
                  *ngIf="isCarpetEdited(element._id)">
            <mat-icon><i class="fa-solid fa-credit-card"></i></mat-icon>
          </button>
          <button mat-icon-button color="warn" (click)="onDeleteCarpet(element)" title="Delete Carpet">
            <mat-icon><i class="fa-solid fa-trash"></i></mat-icon>
          </button>
        </td>
      </ng-container>

      <tr mat-header-row *matHeaderRowDef="displayedColumns"></tr>
      <tr mat-row *matRowDef="let row; columns: displayedColumns;"></tr>
    </table>
  </div>
  <mat-paginator [pageSizeOptions]="[50, 10, 5,25, 100]" aria-label="Select page of users"></mat-paginator>
</fieldset>
  </section>
</div>
