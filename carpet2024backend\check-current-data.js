const mongoose = require('mongoose');

// Connect to MongoDB Atlas
const DB_URL = "mongodb+srv://infosarthaktech:<EMAIL>/test";

mongoose.connect(DB_URL, {
  serverSelectionTimeoutMS: 30000,
  socketTimeoutMS: 45000,
  connectTimeoutMS: 30000
});

// Use the actual model
const CarpetReceived = require('./src/model/phase-4/carpetReceived');

async function checkCurrentData() {
  try {
    console.log('🔍 Checking current data...');
    
    // Check first few regular entries
    const regularEntries = await CarpetReceived.find({
      $and: [
        { receiveNo: { $not: { $regex: '^H-', $options: 'i' } } },
        { receiveNo: { $not: { $regex: '^March', $options: 'i' } } },
        { weaverName: { $not: { $regex: 'stock march', $options: 'i' } } }
      ]
    }).limit(3);
    
    console.log(`📊 Found ${regularEntries.length} regular entries (showing first 3):`);
    
    for (const entry of regularEntries) {
      console.log(`\n--- ${entry.receiveNo} ---`);
      console.log(`rate: ${entry.rate}`);
      console.log(`amount: ${entry.amount}`);
      console.log(`area: ${entry.area}`);
      if (entry.issueNo) {
        console.log(`issueNo.rate: ${entry.issueNo.rate}`);
        console.log(`issueNo.amount: ${entry.issueNo.amount}`);
      }
    }
    
  } catch (error) {
    console.error('❌ Error:', error);
  } finally {
    mongoose.connection.close();
  }
}

checkCurrentData();
