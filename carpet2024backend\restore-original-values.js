const mongoose = require('mongoose');

// Connect to MongoDB Atlas
const DB_URL = "mongodb+srv://infosarthaktech:<EMAIL>/test";

mongoose.connect(DB_URL, {
  serverSelectionTimeoutMS: 30000,
  socketTimeoutMS: 45000,
  connectTimeoutMS: 30000
});

// Use the actual model
const CarpetReceived = require('./src/model/phase-4/carpetReceived');

// Original data mapping based on the previous output
const originalData = {
  'K-2400001': { rate: '190.00', amount: '435.1' },
  'K-2400002': { rate: '190.00', amount: '435.1' },
  'K-2400003': { rate: '190.00', amount: '435.1' },
  'K-2400004': { rate: '190.00', amount: '435.1' },
  'K-2400005': { rate: '190.00', amount: '435.1' },
  'K-2400006': { rate: '190.00', amount: '435.1' },
  'K-2400007': { rate: '220.00', amount: '481.8' },
  'K-2400008': { rate: '220.00', amount: '481.8' },
  'K-2400009': { rate: '220.00', amount: '481.8' },
  'K-2400010': { rate: '220.00', amount: '481.8' },
  'K-2400011': { rate: '220.00', amount: '481.8' },
  'K-2400012': { rate: '220.00', amount: '481.8' },
  'K-2400013': { rate: '400.00', amount: '17052' },
  'K-2400014': { rate: '190.00', amount: '435.1' },
  'K-2400015': { rate: '190.00', amount: '435.1' },
  'K-2400016': { rate: '190.00', amount: '435.1' },
  'K-2400017': { rate: '190.00', amount: '435.1' },
  'K-2400018': { rate: '190.00', amount: '435.1' },
  'K-2400019': { rate: '190.00', amount: '435.1' },
  'K-2400020': { rate: '190.00', amount: '435.1' },
  'K-2400021': { rate: '190.00', amount: '435.1' },
  'K-2400022': { rate: '190.00', amount: '435.1' },
  'K-2400023': { rate: '190.00', amount: '435.1' },
  'K-2400024': { rate: '380.00', amount: '16199.400000000001' },
  'K-2400025': { rate: '226.00', amount: '20039.420000000002' },
  'K-2400026': { rate: '500.00', amount: '7415' },
  'K-2400027': { rate: '190.00', amount: '435.1' },
  'K-2400028': { rate: '190.00', amount: '435.1' },
  'K-2400029': { rate: '190.00', amount: '435.1' },
  'K-2400030': { rate: '190.00', amount: '435.1' },
  'K-2400031': { rate: '500.00', amount: '2795' },
  'K-2400032': { rate: '430.00', amount: '6376.9' },
  'K-2400033': { rate: '500.00', amount: '8395' },
  'K-2400034': { rate: '500.00', amount: '5065' },
  'K-2400035': { rate: '430.00', amount: '18330.9' },
  'K-2400036': { rate: '475.00', amount: '11005.75' },
  'K-2400037': { rate: '225.00', amount: '9591.75' },
  'K-2400038': { rate: '220.00', amount: '13411.2' },
  'K-2400039': { rate: '500.00', amount: '2795' },
  'K-2400040': { rate: '430.00', amount: '4906.3' },
  'K-2400041': { rate: '430.00', amount: '4906.3' },
  'K-2400042': { rate: '500.00', amount: '11585' },
  'K-2400043': { rate: '500.00', amount: '12595' },
  'K-2400044': { rate: '180.00', amount: '5101.2' },
  'K-2400045': { rate: '180.00', amount: '10972.8' },
  'K-2400046': { rate: '390.00', amount: '16625.7' },
  'K-2400047': { rate: '430.00', amount: '18330.9' },
  'K-2400048': { rate: '443.00', amount: '18885.09' },
  'K-2400049': { rate: '200.00', amount: '438' },
  'K-2400050': { rate: '200.00', amount: '438' },
  'K-2400051': { rate: '23.00', amount: '50.37' }
};

async function restoreOriginalValues() {
  try {
    console.log('🔍 Restoring original values for regular carpet entries...');
    
    let updatedCount = 0;
    
    for (const [receiveNo, values] of Object.entries(originalData)) {
      const updateData = {
        rate: values.rate,
        amount: values.amount,
        'issueNo.rate': values.rate,
        'issueNo.amount': values.amount
      };

      const result = await CarpetReceived.updateOne(
        { receiveNo: receiveNo },
        { $set: updateData }
      );

      if (result.modifiedCount > 0) {
        updatedCount++;
        console.log(`✅ Restored: ${receiveNo} - Rate: ${values.rate}, Amount: ${values.amount}`);
      }
    }
    
    console.log(`🎉 Successfully restored ${updatedCount} entries`);
    
  } catch (error) {
    console.error('❌ Error:', error);
  } finally {
    mongoose.connection.close();
  }
}

restoreOriginalValues();
