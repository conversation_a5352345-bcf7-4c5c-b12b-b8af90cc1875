const mongoose = require('mongoose');

// Connect to MongoDB Atlas
const DB_URL = "mongodb+srv://infosarthaktech:<EMAIL>/test";

mongoose.connect(DB_URL, {
  serverSelectionTimeoutMS: 30000,
  socketTimeoutMS: 45000,
  connectTimeoutMS: 30000
});

// Use the actual model
const CarpetReceived = require('./src/model/phase-4/carpetReceived');

async function restoreReceivedCarpetData() {
  try {
    console.log('🔍 Finding regular carpet entries (non-Stock March)...');
    
    // Find regular carpet entries (NOT Stock March)
    const regularEntries = await CarpetReceived.find({
      $and: [
        { receiveNo: { $not: { $regex: '^H-', $options: 'i' } } },
        { receiveNo: { $not: { $regex: '^March', $options: 'i' } } },
        { weaverName: { $not: { $regex: 'stock march', $options: 'i' } } }
      ]
    });
    
    console.log(`📊 Found ${regularEntries.length} regular carpet entries`);
    
    let updatedCount = 0;
    
    for (const entry of regularEntries) {
      let needsUpdate = false;
      const updateData = {};
      
      // Restore rate from issueNo.rate if it exists and main rate is 0
      if (entry.rate === 0 && entry.issueNo && entry.issueNo.rate && entry.issueNo.rate !== '0') {
        updateData.rate = entry.issueNo.rate;
        needsUpdate = true;
        console.log(`💰 Restoring rate: ${entry.receiveNo} - Setting rate to "${entry.issueNo.rate}"`);
      }
      
      // Calculate amount from area and rate if needed
      if (entry.amount === 0 && entry.area && updateData.rate) {
        // Extract numeric value from area (e.g., "2.29 Ft" -> 2.29)
        const areaMatch = entry.area.match(/(\d+\.?\d*)/);
        if (areaMatch) {
          const areaValue = parseFloat(areaMatch[1]);
          const rateValue = parseFloat(updateData.rate);
          const calculatedAmount = (areaValue * rateValue).toFixed(1);
          updateData.amount = calculatedAmount;
          needsUpdate = true;
          console.log(`💵 Calculating amount: ${entry.receiveNo} - ${areaValue} x ${rateValue} = ${calculatedAmount}`);
          
          // Also update issueNo.amount
          if (entry.issueNo) {
            updateData['issueNo.amount'] = calculatedAmount;
          }
        }
      }
      
      // If amount is 0 but issueNo.amount exists, restore from there
      if (entry.amount === 0 && entry.issueNo && entry.issueNo.amount && entry.issueNo.amount !== '0') {
        updateData.amount = entry.issueNo.amount;
        needsUpdate = true;
        console.log(`💵 Restoring amount: ${entry.receiveNo} - Setting amount to "${entry.issueNo.amount}"`);
      }

      if (needsUpdate) {
        await CarpetReceived.updateOne(
          { _id: entry._id },
          { $set: updateData }
        );

        updatedCount++;
        console.log(`✅ Restored: ${entry.receiveNo}`);
      }
    }
    
    console.log(`🎉 Successfully restored ${updatedCount} entries out of ${regularEntries.length} found`);
    
  } catch (error) {
    console.error('❌ Error:', error);
  } finally {
    mongoose.connection.close();
  }
}

restoreReceivedCarpetData();
