const mongoose = require('mongoose');

// Connect to MongoDB Atlas
const DB_URL = "mongodb+srv://infosarthaktech:<EMAIL>/test";

mongoose.connect(DB_URL, {
  serverSelectionTimeoutMS: 30000,
  socketTimeoutMS: 45000,
  connectTimeoutMS: 30000
});

// Use the actual model
const CarpetReceived = require('./src/model/phase-4/carpetReceived');

async function update0x5To210x51() {
  try {
    console.log('🔍 Updating Stock March entries from "0 X 5" to "2.10 X 5.1"...');
    
    // Find Stock March entries with "0 X 5" size
    const entries0x5 = await CarpetReceived.find({
      $and: [
        {
          $or: [
            { receiveNo: { $regex: '^H-', $options: 'i' } },
            { receiveNo: { $regex: '^March', $options: 'i' } },
            { weaverName: { $regex: 'stock march', $options: 'i' } }
          ]
        },
        { size: "0 X 5" }
      ]
    });
    
    console.log(`📊 Found ${entries0x5.length} entries to update`);
    
    let updatedCount = 0;
    
    for (const entry of entries0x5) {
      const updateData = {
        size: "2.10 X 5.1"
      };

      // Update issueNo object if it exists
      if (entry.issueNo) {
        updateData['issueNo.size.sizeInYard'] = "2.10 X 5.1";
        updateData['issueNo.size.sizeinMeter'] = "2.10 X 5.1";
      }

      await CarpetReceived.updateOne(
        { _id: entry._id },
        { $set: updateData }
      );

      updatedCount++;
      console.log(`✅ Updated: ${entry.receiveNo} - Size: "0 X 5" -> "2.10 X 5.1"`);
    }
    
    console.log(`🎉 Successfully updated ${updatedCount} entries from "0 X 5" to "2.10 X 5.1"`);
    
  } catch (error) {
    console.error('❌ Error:', error);
  } finally {
    mongoose.connection.close();
  }
}

update0x5To210x51();
