const mongoose = require('mongoose');

async function createNonUniqueIndexes() {
  try {
    // Connect to MongoDB
    await mongoose.connect("mongodb+srv://infosarthaktech:<EMAIL>/test", {
      serverSelectionTimeoutMS: 30000,
      socketTimeoutMS: 45000,
      connectTimeoutMS: 30000
    });
    console.log('Connected to MongoDB Atlas');

    // Get the collection
    const collection = mongoose.connection.db.collection('exportpackinglists');
    
    // Create non-unique indexes
    console.log('Creating non-unique index on invoiceNo...');
    await collection.createIndex({ invoiceNo: 1 }, { unique: false });
    
    console.log('Creating non-unique index on baleNo...');
    await collection.createIndex({ baleNo: 1 }, { unique: false });
    
    // Verify indexes after changes
    const updatedIndexes = await collection.indexes();
    console.log('Updated indexes:', JSON.stringify(updatedIndexes, null, 2));
    
    console.log('Non-unique indexes created successfully');
  } catch (error) {
    console.error('Error:', error);
  } finally {
    // Close the connection
    await mongoose.disconnect();
    console.log('Disconnected from MongoDB');
  }
}

// Run the function
createNonUniqueIndexes();
