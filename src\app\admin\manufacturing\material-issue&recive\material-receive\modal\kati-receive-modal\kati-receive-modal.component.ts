import { Component, OnInit, Inject } from '@angular/core';
import { MAT_DIALOG_DATA, MatDialogRef } from '@angular/material/dialog';
import { FormBuilder, FormGroup, FormArray } from '@angular/forms';

import Swal from 'sweetalert2';
import { ManufactureService } from '../../../../../../services/manufacture.service';

@Component({
  selector: 'app-kati-receive-modal',
  templateUrl: './kati-receive-modal.component.html',
  styleUrls: ['./kati-receive-modal.component.css']
})
export class KatiReceiveModalComponent implements OnInit {
  katiReceiveForm!: FormGroup;
  displayedColumns: string[] = ['srNo', 'colour', 'lagat', 'carpetLagat', 'tIssued', 'receive', 'totalLagat'];
  materialData: any[] = [];
  totalReceive: number = 0;
  materialType: string = 'kati';
  materialName: string = 'Kati';

  constructor(
    private fb: FormBuilder,
    private manufactureService: ManufactureService,
    public dialogRef: MatDialogRef<KatiReceiveModalComponent>,
    @Inject(MAT_DIALOG_DATA) public data: any
  ) {}

  ngOnInit(): void {
    // Initialize material type and name from data
    this.materialType = this.data.materialType || 'kati';
    this.materialName = this.data.materialName || 'Kati';

    this.initializeForm();
    this.loadMaterialData();
  }

  initializeForm(): void {
    this.katiReceiveForm = this.fb.group({
      katiItems: this.fb.array([])
    });
  }

  get katiItems(): FormArray {
    return this.katiReceiveForm.get('katiItems') as FormArray;
  }

  loadMaterialData(): void {
    console.log(`🔍 Loading ${this.materialName} data for:`, this.data);

    if (!this.data.issueNo || !this.data.weaver) {
      console.log('⚠️ Missing required data');
      return;
    }

    // Get material issues for this weaver and issue
    this.manufactureService.getAllMaterialIssues().subscribe({
      next: (materialIssues: any) => {
        const issuesArray = materialIssues as any[];
        console.log('📦 All material issues:', issuesArray);

        // Filter by weaver and issue number
        const filteredIssues = issuesArray.filter((issue: any) =>
          issue.weaver &&
          issue.weaver._id === this.data.weaver._id &&
          issue.issueNo &&
          issue.issueNo.Br_issueNo === this.data.issueNo.Br_issueNo
        );

        console.log('🎯 Filtered issues:', filteredIssues);

        // Extract material data from all matching issues
        this.materialData = [];
        let srNo = 1;

        filteredIssues.forEach((issue: any) => {
        const materialDataKey = `${this.materialType}Data`;
        if (issue.materials && issue.materials[materialDataKey] && Array.isArray(issue.materials[materialDataKey])) {
          issue.materials[materialDataKey].forEach((materialItem: any) => {
            this.materialData.push({
              srNo: srNo++,
              colour: materialItem.colour ? materialItem.colour.name : '',
              lagat: parseFloat(materialItem.lagat || '0'),
              carpetLagat: parseFloat(materialItem.carpetLagat || '0'),
              tIssued: parseFloat(materialItem.issueValue || '0'),
              receive: 0, // User input
              totalLagat: parseFloat(materialItem.issueValue || '0'), // Will be calculated
              originalData: materialItem
            });
          });
        }
      });

      console.log(`✅ Processed ${this.materialName} data:`, this.materialData);
      this.setupFormArray();
      },
      error: (error) => {
        console.error('❌ Error loading material issues:', error);
        Swal.fire('Error', `Failed to load ${this.materialName} data`, 'error');
      }
    });
  }

  setupFormArray(): void {
    const materialItemsArray = this.fb.array([]);

    this.materialData.forEach((item: any) => {
      const itemGroup = this.fb.group({
        receive: [0]
      });

      // Listen for receive value changes
      itemGroup.get('receive')?.valueChanges.subscribe((receiveValue: any) => {
        const receive = parseFloat(receiveValue || '0');
        const issued = item.tIssued;

        // Calculate total lagat (remaining)
        item.totalLagat = Math.max(0, issued - receive);

        // Update total receive
        this.calculateTotalReceive();
      });

      materialItemsArray.push(itemGroup as any);
    });

    this.katiReceiveForm.setControl('katiItems', materialItemsArray);
  }

  calculateTotalReceive(): void {
    this.totalReceive = 0;

    this.katiItems.controls.forEach((control) => {
      const receiveValue = control.get('receive')?.value || 0;
      this.totalReceive += parseFloat(receiveValue);
    });

    console.log('📊 Total receive calculated:', this.totalReceive);
  }

  onReceiveChange(index: number, event: any): void {
    const receiveValue = parseFloat(event.target.value || '0');
    const item = this.materialData[index];

    // Validate receive value
    if (receiveValue > item.tIssued) {
      Swal.fire('Warning', 'Receive value cannot be greater than issued value', 'warning');
      event.target.value = item.tIssued;
      this.katiItems.at(index).patchValue({ receive: item.tIssued });
      return;
    }

    // Update form control
    this.katiItems.at(index).patchValue({ receive: receiveValue });
  }

  setDigitReceive(event: any): void {
    let value = event.target.value;
    if (value && !isNaN(value)) {
      event.target.value = parseFloat(value).toFixed(3);
    } else if (value === '' || value === null || value === undefined) {
      event.target.value = '0.000';
    }
  }

  onSave(): void {
    console.log(`💾 Saving ${this.materialName} receive data...`);

    // Prepare data to return
    const receiveData = this.materialData.map((item, index) => {
      const receiveValue = this.katiItems.at(index).get('receive')?.value || 0;
      const formattedReceiveValue = receiveValue === 0 || receiveValue === '' || receiveValue === null || receiveValue === undefined
        ? '0.000'
        : parseFloat(receiveValue.toString()).toFixed(3);

      return {
        ...item.originalData,
        receiveValue: formattedReceiveValue
      };
    });

    const result = {
      materialReceiveData: receiveData,
      totalReceive: this.totalReceive,
      materialType: this.materialType
    };

    console.log('✅ Returning data:', result);
    this.dialogRef.close(result);
  }

  onCancel(): void {
    this.dialogRef.close();
  }
}
