const mongoose = require('mongoose');

// Connect to MongoDB Atlas
const DB_URL = "mongodb+srv://infosarthaktech:<EMAIL>/test";

mongoose.connect(DB_URL, {
  serverSelectionTimeoutMS: 30000,
  socketTimeoutMS: 45000,
  connectTimeoutMS: 30000
});

// Use the actual model
const CarpetReceived = require('./src/model/phase-4/carpetReceived');

async function checkReceivedCarpetData() {
  try {
    console.log('🔍 Checking receivedcarpet data...');
    
    // Find entries that are NOT Stock March (regular carpet entries)
    const regularEntries = await CarpetReceived.find({
      $and: [
        { receiveNo: { $not: { $regex: '^H-', $options: 'i' } } },
        { receiveNo: { $not: { $regex: '^March', $options: 'i' } } },
        { weaverName: { $not: { $regex: 'stock march', $options: 'i' } } }
      ]
    }).limit(10);
    
    console.log(`📊 Found ${regularEntries.length} regular carpet entries (showing first 10)`);
    
    for (const entry of regularEntries) {
      console.log('\n--- Entry ---');
      console.log(`receiveNo: ${entry.receiveNo}`);
      console.log(`weaverName: ${entry.weaverName}`);
      console.log(`size: ${entry.size}`);
      console.log(`rate: ${entry.rate || 'undefined'}`);
      console.log(`amount: ${entry.amount}`);
      console.log(`area: ${entry.area}`);
      
      if (entry.issueNo && typeof entry.issueNo === 'object') {
        console.log('issueNo object:');
        console.log(`  size: ${JSON.stringify(entry.issueNo.size)}`);
        console.log(`  rate: ${entry.issueNo.rate}`);
        console.log(`  amount: ${entry.issueNo.amount}`);
      }
    }
    
    // Check for entries with problematic sizes
    const problematicSizes = await CarpetReceived.find({
      $and: [
        { receiveNo: { $not: { $regex: '^H-', $options: 'i' } } },
        { receiveNo: { $not: { $regex: '^March', $options: 'i' } } },
        { weaverName: { $not: { $regex: 'stock march', $options: 'i' } } },
        {
          $or: [
            { size: { $regex: /\d+\s+\d+\s*[Xx]\s*\d+\s+\d+/ } }, // "5 6 x 7 9" pattern
            { size: { $regex: /\d{2,}[Xx]\d{2,}/ } }, // "56x79" pattern without decimals
            { size: { $not: { $regex: /\d+(?:\.\d+)?\s*[Xx]\s*\d+(?:\.\d+)?/ } } } // Not proper format
          ]
        }
      ]
    }).limit(5);
    
    console.log(`\n🔧 Found ${problematicSizes.length} entries with problematic sizes:`);
    for (const entry of problematicSizes) {
      console.log(`${entry.receiveNo}: "${entry.size}"`);
    }
    
  } catch (error) {
    console.error('❌ Error:', error);
  } finally {
    mongoose.connection.close();
  }
}

checkReceivedCarpetData();
