// <PERSON>ript to drop all unique indexes on the CarpetOrderissue collection
const mongoose = require('mongoose');

// Connect to MongoDB directly with the connection string
mongoose.connect("mongodb+srv://infosarthaktech:<EMAIL>/test", {
  serverSelectionTimeoutMS: 30000,
  socketTimeoutMS: 45000,
  connectTimeoutMS: 30000
})
  .then(() => {
    console.log('Connected to MongoDB');

    // Get the CarpetOrderissue collection
    const db = mongoose.connection;
    const collection = db.collection('carpetorderissues');

    // List all indexes on the collection
    collection.indexes()
      .then(indexes => {
        console.log('Current indexes:', JSON.stringify(indexes, null, 2));

        // Find all unique indexes
        const uniqueIndexes = indexes.filter(index =>
          index.unique === true && index.name !== '_id_'
        );

        if (uniqueIndexes.length > 0) {
          console.log(`Found ${uniqueIndexes.length} unique indexes. Dropping them all...`);

          // Create an array of promises to drop each unique index
          const dropPromises = uniqueIndexes.map(index => {
            console.log(`Dropping index: ${index.name}`);
            return collection.dropIndex(index.name);
          });

          return Promise.all(dropPromises)
            .then(() => {
              console.log('Successfully dropped all unique indexes');
              mongoose.disconnect();
            });
        } else {
          console.log('No unique indexes found');
          mongoose.disconnect();
        }
      })
      .catch(err => {
        console.error('Error listing indexes:', err);
        mongoose.disconnect();
      });
  })
  .catch(err => {
    console.error('Error connecting to MongoDB:', err);
  });
