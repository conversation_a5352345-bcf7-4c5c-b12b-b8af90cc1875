const mongoose = require('mongoose');

// Connect to MongoDB Atlas
const DB_URL = "mongodb+srv://infosarthaktech:<EMAIL>/test";

mongoose.connect(DB_URL, {
  serverSelectionTimeoutMS: 30000,
  socketTimeoutMS: 45000,
  connectTimeoutMS: 30000
});

// Use the actual model
const CarpetReceived = require('./src/model/phase-4/carpetReceived');

async function check0x5Entries() {
  try {
    console.log('🔍 Checking Stock March entries with "0 X 5" size...');
    
    // Find Stock March entries with "0 X 5" size
    const entries0x5 = await CarpetReceived.find({
      $and: [
        {
          $or: [
            { receiveNo: { $regex: '^H-', $options: 'i' } },
            { receiveNo: { $regex: '^March', $options: 'i' } },
            { weaverName: { $regex: 'stock march', $options: 'i' } }
          ]
        },
        { size: "0 X 5" }
      ]
    });
    
    console.log(`📊 Found ${entries0x5.length} Stock March entries with "0 X 5" size:`);
    
    for (const entry of entries0x5) {
      console.log(`${entry.receiveNo}: "${entry.size}"`);
    }
    
  } catch (error) {
    console.error('❌ Error:', error);
  } finally {
    mongoose.connection.close();
  }
}

check0x5Entries();
