const mongoose = require('mongoose');

async function checkIndexes() {
  try {
    // Connect to MongoDB
    await mongoose.connect("mongodb+srv://infosarthaktech:<EMAIL>/test", {
      serverSelectionTimeoutMS: 30000,
      socketTimeoutMS: 45000,
      connectTimeoutMS: 30000
    });
    console.log('Connected to MongoDB Atlas');

    // Get the collection
    const collection = mongoose.connection.db.collection('exportpackinglists');
    
    // List all indexes
    const indexes = await collection.indexes();
    console.log('Current indexes:', JSON.stringify(indexes, null, 2));
    
    // Check if there's a unique index on baleNo
    const hasBaleNoUniqueIndex = indexes.some(index => 
      index.key && index.key.baleNo === 1 && index.unique === true
    );
    
    if (hasBaleNoUniqueIndex) {
      console.log('WARNING: There is still a unique index on baleNo field!');
      
      // Drop the unique index
      for (const index of indexes) {
        if (index.key && index.key.baleNo === 1 && index.unique === true) {
          console.log('Dropping unique index on baleNo:', index.name);
          await collection.dropIndex(index.name);
          console.log('Unique index dropped successfully');
        }
      }
      
      // Create a new non-unique index
      await collection.createIndex({ baleNo: 1 }, { unique: false });
      console.log('Created new non-unique index on baleNo');
      
      // Verify indexes after changes
      const updatedIndexes = await collection.indexes();
      console.log('Updated indexes:', JSON.stringify(updatedIndexes, null, 2));
    } else {
      console.log('No unique index on baleNo field found. All good!');
    }
  } catch (error) {
    console.error('Error:', error);
  } finally {
    // Close the connection
    await mongoose.disconnect();
    console.log('Disconnected from MongoDB');
  }
}

// Run the function
checkIndexes();
