const express = require('express');
const router = express.Router();
const materialIssueController = require('../../controller/manifacturing/materialIssue-controller');

// Create a new material issue
router.post('/materialIssue', materialIssueController.createMaterialIssue);

// Get all material issues
router.get('/materialIssue', materialIssueController.getAllMaterialIssues);

// Get material issue by ID
router.get('/materialIssue/:id', materialIssueController.getMaterialIssueById);

// Update material issue by ID
router.patch('/materialIssue/:id', materialIssueController.updateMaterialIssue);

// Update material issue field
router.patch('/materialIssue/:id/updateField', materialIssueController.updateMaterialIssueField);

// Delete material issue field
router.delete('/delete-materialIssue/:id', materialIssueController.deleteMaterialIssueField);

// Delete material issue by ID
router.delete('/materialIssue/:id', materialIssueController.deleteMaterialIssue);

// Get material issue by challan number
router.get('/materialIssue/challan/:challanNo', materialIssueController.getMaterialIssuesByChallanNo);

// Get material issues by weaver
router.get('/materialIssue/weaver/:weaver', materialIssueController.getMaterialIssuesByWeaver);

// Get material issues by date range
router.get('/materialIssue/dateRange', materialIssueController.getMaterialIssuesByDateRange);

// Get material issues by issue number
router.get('/materialIssue/issueNo/:issueNoId', materialIssueController.getMaterialIssuesByIssueNo);

module.exports = router;
