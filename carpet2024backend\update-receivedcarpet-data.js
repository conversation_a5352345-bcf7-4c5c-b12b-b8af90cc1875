const mongoose = require('mongoose');

// Connect to MongoDB Atlas
const DB_URL = "mongodb+srv://infosarthaktech:<EMAIL>/test";

mongoose.connect(DB_URL, {
  serverSelectionTimeoutMS: 30000,
  socketTimeoutMS: 45000,
  connectTimeoutMS: 30000
});

// Use the actual model
const CarpetReceived = require('./src/model/phase-4/carpetReceived');

// Format size function - same as Stock March script
const formatSize = (size) => {
  if (!size) return size;

  // Handle patterns like "5 6 x 7 9" -> "5.6 X 7.9"
  const spacedPattern = /(\d+)\s+(\d+)\s*[Xx]\s*(\d+)\s+(\d+)/;
  const spacedMatch = size.match(spacedPattern);

  if (spacedMatch) {
    const width1 = spacedMatch[1];
    const width2 = spacedMatch[2];
    const length1 = spacedMatch[3];
    const length2 = spacedMatch[4];

    return `${width1}.${width2} X ${length1}.${length2}`;
  }

  // Handle normal patterns and ensure proper spacing like "7 X 42"
  const sizePattern = /(\d+(?:\.\d+)?)\s*[Xx]\s*(\d+(?:\.\d+)?)/;
  const match = size.match(sizePattern);

  if (match) {
    const width = match[1];
    const length = match[2];

    // Only format multi-digit numbers without decimal
    const formattedWidth = (width.includes('.') || width.length === 1) ? width : addDecimalToMultiDigit(width);
    const formattedLength = (length.includes('.') || length.length === 1) ? length : addDecimalToMultiDigit(length);

    // Ensure neat spacing: single space before and after X
    return `${formattedWidth} X ${formattedLength}`;
  }

  return size;
};

const addDecimalToMultiDigit = (number) => {
  if (number.length <= 1) return number;
  
  const firstDigit = number.charAt(0);
  const remainingDigits = number.substring(1);
  
  if (remainingDigits === '0') {
    return firstDigit + '.0';
  }
  
  return firstDigit + '.' + remainingDigits;
};

async function updateReceivedCarpetData() {
  try {
    console.log('🔍 Finding regular carpet entries (non-Stock March)...');
    
    // Find regular carpet entries (NOT Stock March)
    const regularEntries = await CarpetReceived.find({
      $and: [
        { receiveNo: { $not: { $regex: '^H-', $options: 'i' } } },
        { receiveNo: { $not: { $regex: '^March', $options: 'i' } } },
        { weaverName: { $not: { $regex: 'stock march', $options: 'i' } } }
      ]
    });
    
    console.log(`📊 Found ${regularEntries.length} regular carpet entries`);
    
    let updatedCount = 0;
    
    for (const entry of regularEntries) {
      let needsUpdate = false;
      const updateData = {};
      
      // 1. Fix size field - get from issueNo.size.sizeInYard if main size is undefined
      if (!entry.size && entry.issueNo && entry.issueNo.size && entry.issueNo.size.sizeInYard) {
        const formattedSize = formatSize(entry.issueNo.size.sizeInYard);
        updateData.size = formattedSize;
        needsUpdate = true;
        console.log(`📏 Size fix: ${entry.receiveNo} - Setting size to "${formattedSize}"`);
      } else if (entry.size) {
        const formattedSize = formatSize(entry.size);
        if (formattedSize !== entry.size) {
          updateData.size = formattedSize;
          needsUpdate = true;
          console.log(`📏 Size format: ${entry.receiveNo} - "${entry.size}" -> "${formattedSize}"`);
        }
      }
      
      // 2. Fix rate field - add rate field if it doesn't exist but issueNo.rate exists
      if (!entry.rate && entry.issueNo && entry.issueNo.rate) {
        updateData.rate = entry.issueNo.rate;
        needsUpdate = true;
        console.log(`💰 Rate fix: ${entry.receiveNo} - Setting rate to "${entry.issueNo.rate}"`);
      }
      
      // 3. Fix issueNo.amount - set it from main amount if it's undefined
      if (entry.issueNo && !entry.issueNo.amount && entry.amount) {
        updateData['issueNo.amount'] = entry.amount;
        needsUpdate = true;
        console.log(`💵 Amount fix: ${entry.receiveNo} - Setting issueNo.amount to "${entry.amount}"`);
      }
      
      // 4. Update issueNo.size fields to match main size
      if (entry.issueNo && entry.issueNo.size && updateData.size) {
        updateData['issueNo.size.sizeInYard'] = updateData.size;
        updateData['issueNo.size.sizeinMeter'] = updateData.size;
        needsUpdate = true;
      }

      if (needsUpdate) {
        await CarpetReceived.updateOne(
          { _id: entry._id },
          { $set: updateData }
        );

        updatedCount++;
        console.log(`✅ Updated: ${entry.receiveNo}`);
      }
    }
    
    console.log(`🎉 Successfully updated ${updatedCount} entries out of ${regularEntries.length} found`);
    
  } catch (error) {
    console.error('❌ Error:', error);
  } finally {
    mongoose.connection.close();
  }
}

updateReceivedCarpetData();
