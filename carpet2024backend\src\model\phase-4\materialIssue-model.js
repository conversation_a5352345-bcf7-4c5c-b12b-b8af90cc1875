const mongoose = require("mongoose");

const materialIssueSchema = new mongoose.Schema(
  {
    challanNo: {
      type: String,
      required: true,
      unique: true
    },
    date: {
     type: Date,
      required: true
    },
    weaver: {
      type: mongoose.Schema.Types.ObjectId,
      ref: "WeaverEmployee",
      required: true,
    },
    issueNo: {
      type: mongoose.Schema.Types.ObjectId,
      ref: "CarpetOrderissue",
      required: true
    },
    issueAreaInYard: {
      type: String,
      required: true
    },
    materials: {
      kati: {
        description: {
            type: mongoose.Schema.Types.ObjectId,
            ref: 'RawMaterialGroup',
            required: false, // Make it optional for now
        },
        lagat: String,
        issue:{ type: String, default: "0.000" },
      },
      tana: {
        description: {
            type: mongoose.Schema.Types.ObjectId,
            ref: 'RawMaterialGroup',
            required: false, // Make it optional for now
        },
        lagat: String,
        issue: { type: String, default: "0.000" },
      },
      soot: {
        description: {
            type: mongoose.Schema.Types.ObjectId,
            ref: 'RawMaterialGroup',
            required: false, // Make it optional for now
        },
         lagat: String,
        issue: { type: String, default: "0.000" },
      },
      thari: {
        description: {
            type: mongoose.Schema.Types.ObjectId,
            ref: 'RawMaterialGroup',
            required: false, // Make it optional for now
        },
        lagat: String,
        issue:{ type: String, default: "0.000" },
      },
      silk: {
        description: {
            type: mongoose.Schema.Types.ObjectId,
            ref: 'RawMaterialGroup',
            required: false, // Make it optional for now
        },
        lagat: String,
        issue: { type: String, default: "0.000" },
      },
      other: {
        description: {
            type: mongoose.Schema.Types.ObjectId,
            ref: 'RawMaterialGroup',
            required: false, // Make it optional for now
        },
        lagat: String,
        issue: { type: String, default: "0.000" },
      }
    },
     katiData: [{
        colour: {
          type: mongoose.Schema.Types.ObjectId,
          ref: 'Color',
          required: false // Make it optional for now
        },
      lagat: String,
      carpetLagat: String,
      issueValue: String,
      toIssueValue: { type: String, default: "0.000" },
    }],
  },
  {
    timestamps: true
  }
);

module.exports = mongoose.model('MaterialIssue', materialIssueSchema);
