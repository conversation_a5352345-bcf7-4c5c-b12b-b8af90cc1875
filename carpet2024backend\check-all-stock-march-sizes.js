const mongoose = require('mongoose');

// Connect to MongoDB Atlas
const DB_URL = "mongodb+srv://infosarthaktech:<EMAIL>/test";

mongoose.connect(DB_URL, {
  serverSelectionTimeoutMS: 30000,
  socketTimeoutMS: 45000,
  connectTimeoutMS: 30000
});

// Use the actual model
const CarpetReceived = require('./src/model/phase-4/carpetReceived');

async function checkAllStockMarchSizes() {
  try {
    console.log('🔍 Checking all unique sizes in Stock March entries...');
    
    // Find all Stock March entries
    const stockMarchEntries = await CarpetReceived.find({
      $or: [
        { receiveNo: { $regex: '^H-', $options: 'i' } },
        { receiveNo: { $regex: '^March', $options: 'i' } },
        { weaverName: { $regex: 'stock march', $options: 'i' } }
      ]
    });
    
    // Get unique sizes
    const uniqueSizes = [...new Set(stockMarchEntries.map(entry => entry.size))];
    uniqueSizes.sort();
    
    console.log(`📊 Found ${uniqueSizes.length} unique sizes in Stock March entries:`);
    
    for (const size of uniqueSizes) {
      const count = stockMarchEntries.filter(entry => entry.size === size).length;
      console.log(`"${size}": ${count} entries`);
    }
    
    // Check specifically for sizes containing "3" and "5"
    console.log('\n🔍 Sizes containing "3" and "5":');
    const sizes3and5 = uniqueSizes.filter(size => 
      size && size.includes('3') && size.includes('5')
    );
    
    for (const size of sizes3and5) {
      const count = stockMarchEntries.filter(entry => entry.size === size).length;
      console.log(`"${size}": ${count} entries`);
    }
    
  } catch (error) {
    console.error('❌ Error:', error);
  } finally {
    mongoose.connection.close();
  }
}

checkAllStockMarchSizes();
