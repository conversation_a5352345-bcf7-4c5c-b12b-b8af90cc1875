const MaterialIssue = require('../../model/phase-4/materialIssue-model');

const createMaterialIssue = async (data) => {
  const newMaterialIssue = new MaterialIssue(data);
  return await newMaterialIssue.save();
};

const getAllMaterialIssues = async () => {
  return await MaterialIssue.find()
     .populate({
      path: 'weaver',
      populate: [
        { path: 'branch' },
      ]
    })
     .populate({
      path: 'issueNo',
      populate: [
        { path: 'quality' },
        { path: 'design' },
        { path: 'branch' },
        { path: 'size' },
        { path: 'buyerOrder' },
        { path: 'weaver' },
      ]
    })
    .populate('materials.kati.description')
    .populate('materials.tana.description')
    .populate('materials.soot.description')
    .populate('materials.thari.description')
    .populate('materials.silk.description')
    .populate('materials.other.description')
    .populate('katiData.colour')
    .sort({ createdAt: -1 });
};

const getMaterialIssueById = async (id) => {
  return await MaterialIssue.findById(id);
};

const updateMaterialIssue = async (id, data) => {
  return await MaterialIssue.findByIdAndUpdate(id, data, { new: true });
};

const updateMaterialIssueField = async (id, field, value) => {
  const updateData = {};
  updateData[field] = value;
  return await MaterialIssue.findByIdAndUpdate(id, updateData, { new: true });
};

const deleteMaterialIssueField = async (id, field) => {
  const unsetData = {};
  unsetData[field] = "";
  return await MaterialIssue.findByIdAndUpdate(id, { $unset: unsetData }, { new: true });
};

const deleteMaterialIssue = async (id) => {
  return await MaterialIssue.findByIdAndDelete(id);
};

const getMaterialIssuesByChallanNo = async (challanNo) => {
  return await MaterialIssue.findOne({ challanNo });
};

const getMaterialIssuesByWeaver = async (weaver) => {
  return await MaterialIssue.find({ weaver }).sort({ createdAt: -1 });
};

const getMaterialIssuesByDateRange = async (startDate, endDate) => {
  return await MaterialIssue.find({
    date: {
      $gte: startDate,
      $lte: endDate
    }
  }).sort({ createdAt: -1 });
};

const getMaterialIssuesByIssueNo = async (issueNoId) => {
  return await MaterialIssue.find({ issueNo: issueNoId })
    .populate({
      path: 'weaver',
      populate: [
        { path: 'branch' },
      ]
    })
    .populate({
      path: 'issueNo',
      populate: [
        { path: 'quality' },
        { path: 'design' },
        { path: 'branch' },
        { path: 'size' },
        { path: 'buyerOrder' },
        { path: 'weaver' },
      ]
    })
    .populate('materials.kati.description')
    .populate('materials.tana.description')
    .populate('materials.soot.description')
    .populate('materials.thari.description')
    .populate('materials.silk.description')
    .populate('materials.other.description')
    .populate('katiData.colour')
    .sort({ createdAt: -1 });
};

module.exports = {
  createMaterialIssue,
  getAllMaterialIssues,
  getMaterialIssueById,
  updateMaterialIssue,
  updateMaterialIssueField,
  deleteMaterialIssueField,
  deleteMaterialIssue,
  getMaterialIssuesByChallanNo,
  getMaterialIssuesByWeaver,
  getMaterialIssuesByDateRange,
  getMaterialIssuesByIssueNo
};
