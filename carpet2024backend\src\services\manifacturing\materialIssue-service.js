const materialIssueRepository = require('../../repositories/manifacturing/materialIssue-repository');

const createMaterialIssue = async (data) => {
  return await materialIssueRepository.createMaterialIssue(data);
};

const getAllMaterialIssues = async () => {
  return await materialIssueRepository.getAllMaterialIssues();
};

const getMaterialIssueById = async (id) => {
  return await materialIssueRepository.getMaterialIssueById(id);
};

const updateMaterialIssue = async (id, data) => {
  return await materialIssueRepository.updateMaterialIssue(id, data);
};

const updateMaterialIssueField = async (id, field, value) => {
  return await materialIssueRepository.updateMaterialIssueField(id, field, value);
};

const deleteMaterialIssueField = async (id, field) => {
  return await materialIssueRepository.deleteMaterialIssueField(id, field);
};

const deleteMaterialIssue = async (id) => {
  return await materialIssueRepository.deleteMaterialIssue(id);
};

const getMaterialIssuesByChallanNo = async (challanNo) => {
  return await materialIssueRepository.getMaterialIssuesByChallanNo(challanNo);
};

const getMaterialIssuesByWeaver = async (weaver) => {
  return await materialIssueRepository.getMaterialIssuesByWeaver(weaver);
};

const getMaterialIssuesByDateRange = async (startDate, endDate) => {
  return await materialIssueRepository.getMaterialIssuesByDateRange(startDate, endDate);
};

const getMaterialIssuesByIssueNo = async (issueNoId) => {
  return await materialIssueRepository.getMaterialIssuesByIssueNo(issueNoId);
};

module.exports = {
  createMaterialIssue,
  getAllMaterialIssues,
  getMaterialIssueById,
  updateMaterialIssue,
  updateMaterialIssueField,
  deleteMaterialIssueField,
  deleteMaterialIssue,
  getMaterialIssuesByChallanNo,
  getMaterialIssuesByWeaver,
  getMaterialIssuesByDateRange,
  getMaterialIssuesByIssueNo
};
