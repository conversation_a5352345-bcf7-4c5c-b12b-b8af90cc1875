const mongoose = require('mongoose');

async function dropUniqueIndex() {
  try {
    // Connect to MongoDB
    await mongoose.connect('mongodb://localhost:27017/test');
    console.log('Connected to MongoDB');

    // Get the collection
    const collection = mongoose.connection.db.collection('exportpackinglists');
    
    // List all indexes
    const indexes = await collection.indexes();
    console.log('Current indexes:', indexes);
    
    // Find and drop the unique index on baleNo
    for (const index of indexes) {
      if (index.key && index.key.baleNo === 1) {
        console.log('Found index on baleNo:', index);
        await collection.dropIndex(index.name);
        console.log(`Dropped index: ${index.name}`);
      }
    }
    
    // Create a new non-unique index on baleNo if needed
    await collection.createIndex({ baleNo: 1 }, { unique: false });
    console.log('Created new non-unique index on baleNo');
    
    // Verify indexes after changes
    const updatedIndexes = await collection.indexes();
    console.log('Updated indexes:', updatedIndexes);
    
    console.log('Index operation completed successfully');
  } catch (error) {
    console.error('Error:', error);
  } finally {
    // Close the connection
    await mongoose.disconnect();
    console.log('Disconnected from MongoDB');
  }
}

// Run the function
dropUniqueIndex();
