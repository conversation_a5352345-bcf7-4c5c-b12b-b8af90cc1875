<div class="kati-modal-container">
  <div class="modal-header">
    <h2 mat-dialog-title>Kati Material Details</h2>
    <button mat-icon-button (click)="onCancel()" class="close-button">
      <mat-icon>close</mat-icon>
    </button>
  </div>

  <div mat-dialog-content class="modal-content">
    <!-- Angular Material Table -->
    <div class="col-md-12" >
      <table mat-table [dataSource]="dataSource" matSort class="mat-elevation-z8" style="width: max-content;">
        <!-- S.No Column -->
        <ng-container matColumnDef="srNo">
          <th mat-header-cell *matHeaderCellDef>Sr.No</th>
          <td mat-cell *matCellDef="let element; let i = index">{{i + 1}}</td>
          <td mat-footer-cell *matFooterCellDef></td>
        </ng-container>

        <!-- Colour Column -->
        <ng-container matColumnDef="colour">
          <th mat-header-cell *matHeaderCellDef>Colour</th>
          <td mat-cell *matCellDef="let element">{{getColourDisplay(element)}}</td>
          <td mat-footer-cell *matFooterCellDef><b>Total</b></td>
        </ng-container>

        <!-- Lagat Column -->
        <ng-container matColumnDef="lagat">
          <th mat-header-cell *matHeaderCellDef>Lagat</th>
          <td mat-cell *matCellDef="let element">{{element.lagat | number:'1.3-3'}}</td>
          <td mat-footer-cell *matFooterCellDef></td>
        </ng-container>

        <!-- Carpet Lagat Column -->
        <ng-container matColumnDef="carpetLagat">
          <th mat-header-cell *matHeaderCellDef>Carpet Lagat</th>
          <td mat-cell *matCellDef="let element">{{calculateCarpetLagat(element.lagat)}}</td>
          <td mat-footer-cell *matFooterCellDef></td>
        </ng-container>

        <!-- Issued Column (read-only, auto-calculated) -->
        <ng-container matColumnDef="issue">
          <th mat-header-cell *matHeaderCellDef>Issued</th>
          <td mat-cell *matCellDef="let element">
            {{ getIssuedDisplay(element) }}
          </td>
          <td mat-footer-cell *matFooterCellDef>{{ totalIssuedSum | number:'1.3-3' }}</td>
        </ng-container>

        <!-- To Issue Column -->
        <ng-container matColumnDef="toIssue">
          <th mat-header-cell *matHeaderCellDef>To Issue</th>
          <td mat-cell *matCellDef="let element">
            <input class="custom-input"
                   [ngModel]="element.toIssueValue"
                   (ngModelChange)="onInputChange(element, 'toIssue', { target: { value: $event } })"
                   placeholder=".000" (blur)="setDigitToIssue($event)"
                   type="text">
          </td>
          <td mat-footer-cell *matFooterCellDef>{{ totalToIssueSum | number:'1.3-3' }}</td>
        </ng-container>

        <tr mat-header-row *matHeaderRowDef="displayedColumns"></tr>
        <tr mat-row *matRowDef="let row; columns: displayedColumns;"></tr>
        <tr mat-footer-row *matFooterRowDef="displayedColumns; sticky: true" class="totals-footer"></tr>
      </table>
    </div>
  </div>

  <div mat-dialog-actions class="modal-actions">
    <button mat-button (click)="onCancel()" class="cancel-button">Cancel</button>
    <button mat-raised-button color="primary" (click)="onSave()" class="save-button">
      {{ isEditMode ? 'Update' : 'Save' }}
    </button>
  </div>
</div>