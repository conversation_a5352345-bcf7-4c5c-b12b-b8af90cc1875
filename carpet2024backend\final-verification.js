const mongoose = require('mongoose');

// Connect to MongoDB Atlas
const DB_URL = "mongodb+srv://infosarthaktech:<EMAIL>/test";

mongoose.connect(DB_URL, {
  serverSelectionTimeoutMS: 30000,
  socketTimeoutMS: 45000,
  connectTimeoutMS: 30000
});

// Use the actual model
const CarpetReceived = require('./src/model/phase-4/carpetReceived');

async function finalVerification() {
  try {
    console.log('🔍 Final verification of all data...\n');
    
    // Check Stock March entries
    console.log('=== STOCK MARCH ENTRIES ===');
    const stockMarchEntries = await CarpetReceived.find({
      $or: [
        { receiveNo: { $regex: '^H-', $options: 'i' } },
        { receiveNo: { $regex: '^March', $options: 'i' } },
        { weaverName: { $regex: 'stock march', $options: 'i' } }
      ]
    }).limit(3);
    
    console.log(`📊 Found ${stockMarchEntries.length} Stock March entries (showing first 3):`);
    for (const entry of stockMarchEntries) {
      console.log(`\n--- ${entry.receiveNo} ---`);
      console.log(`size: ${entry.size}`);
      console.log(`rate: ${entry.rate}`);
      console.log(`amount: ${entry.amount}`);
      if (entry.issueNo) {
        console.log(`issueNo.rate: ${entry.issueNo.rate}`);
        console.log(`issueNo.amount: ${entry.issueNo.amount}`);
      }
    }
    
    // Check Regular entries
    console.log('\n=== REGULAR CARPET ENTRIES ===');
    const regularEntries = await CarpetReceived.find({
      $and: [
        { receiveNo: { $not: { $regex: '^H-', $options: 'i' } } },
        { receiveNo: { $not: { $regex: '^March', $options: 'i' } } },
        { weaverName: { $not: { $regex: 'stock march', $options: 'i' } } }
      ]
    }).limit(3);
    
    console.log(`📊 Found ${regularEntries.length} regular entries (showing first 3):`);
    for (const entry of regularEntries) {
      console.log(`\n--- ${entry.receiveNo} ---`);
      console.log(`size: ${entry.size}`);
      console.log(`rate: ${entry.rate}`);
      console.log(`amount: ${entry.amount}`);
      if (entry.issueNo) {
        console.log(`issueNo.rate: ${entry.issueNo.rate}`);
        console.log(`issueNo.amount: ${entry.issueNo.amount}`);
      }
    }
    
    console.log('\n✅ Verification complete!');
    
  } catch (error) {
    console.error('❌ Error:', error);
  } finally {
    mongoose.connection.close();
  }
}

finalVerification();
